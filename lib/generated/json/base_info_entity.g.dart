import 'package:flutter_kit/generated/json/base/json_convert_content.dart';
import 'package:flutter_kit/src/datasource/models/base_info_entity.dart';

BaseInfoEntity $BaseInfoEntityFromJson(Map<String, dynamic> json) {
  final BaseInfoEntity baseInfoEntity = BaseInfoEntity();
  final String? name = jsonConvert.convert<String>(json['Name']);
  if (name != null) {
    baseInfoEntity.name = name;
  }
  final String? genderCode = jsonConvert.convert<String>(json['GenderCode']);
  if (genderCode != null) {
    baseInfoEntity.genderCode = genderCode;
  }
  final String? nationCode = jsonConvert.convert<String>(json['NationCode']);
  if (nationCode != null) {
    baseInfoEntity.nationCode = nationCode;
  }
  final String? maritalStatusCode = jsonConvert.convert<String>(
      json['MaritalStatusCode']);
  if (maritalStatusCode != null) {
    baseInfoEntity.maritalStatusCode = maritalStatusCode;
  }
  final String? birthday = jsonConvert.convert<String>(json['Birthday']);
  if (birthday != null) {
    baseInfoEntity.birthday = birthday;
  }
  final int? stature = jsonConvert.convert<int>(json['Stature']);
  if (stature != null) {
    baseInfoEntity.stature = stature;
  }
  final String? phone = jsonConvert.convert<String>(json['Phone']);
  if (phone != null) {
    baseInfoEntity.phone = phone;
  }
  final String? email = jsonConvert.convert<String>(json['Email']);
  if (email != null) {
    baseInfoEntity.email = email;
  }
  final String? qQ = jsonConvert.convert<String>(json['QQ']);
  if (qQ != null) {
    baseInfoEntity.qQ = qQ;
  }
  final String? address = jsonConvert.convert<String>(json['Address']);
  if (address != null) {
    baseInfoEntity.address = address;
  }
  final String? computerLevelCode = jsonConvert.convert<String>(
      json['ComputerLevelCode']);
  if (computerLevelCode != null) {
    baseInfoEntity.computerLevelCode = computerLevelCode;
  }
  final String? englishLevelCode = jsonConvert.convert<String>(
      json['EnglishLevelCode']);
  if (englishLevelCode != null) {
    baseInfoEntity.englishLevelCode = englishLevelCode;
  }
  final String? liveAreaId = jsonConvert.convert<String>(json['LiveAreaId']);
  if (liveAreaId != null) {
    baseInfoEntity.liveAreaId = liveAreaId;
  }
  final String? liveAreaName = jsonConvert.convert<String>(
      json['LiveAreaName']);
  if (liveAreaName != null) {
    baseInfoEntity.liveAreaName = liveAreaName;
  }
  final String? liveAreaCascadeName = jsonConvert.convert<String>(
      json['LiveAreaCascadeName']);
  if (liveAreaCascadeName != null) {
    baseInfoEntity.liveAreaCascadeName = liveAreaCascadeName;
  }
  final String? nativePlaceAreaId = jsonConvert.convert<String>(
      json['NativePlaceAreaId']);
  if (nativePlaceAreaId != null) {
    baseInfoEntity.nativePlaceAreaId = nativePlaceAreaId;
  }
  final String? nativePlaceAreaName = jsonConvert.convert<String>(
      json['NativePlaceAreaName']);
  if (nativePlaceAreaName != null) {
    baseInfoEntity.nativePlaceAreaName = nativePlaceAreaName;
  }
  final String? nativePlaceAreaCascadeName = jsonConvert.convert<String>(
      json['NativePlaceAreaCascadeName']);
  if (nativePlaceAreaCascadeName != null) {
    baseInfoEntity.nativePlaceAreaCascadeName = nativePlaceAreaCascadeName;
  }
  final String? educationCode = jsonConvert.convert<String>(
      json['EducationCode']);
  if (educationCode != null) {
    baseInfoEntity.educationCode = educationCode;
  }
  final String? workingAgeCode = jsonConvert.convert<String>(
      json['WorkingAgeCode']);
  if (workingAgeCode != null) {
    baseInfoEntity.workingAgeCode = workingAgeCode;
  }
  final String? graduateSchool = jsonConvert.convert<String>(
      json['GraduateSchool']);
  if (graduateSchool != null) {
    baseInfoEntity.graduateSchool = graduateSchool;
  }
  final String? majorIn = jsonConvert.convert<String>(json['MajorIn']);
  if (majorIn != null) {
    baseInfoEntity.majorIn = majorIn;
  }
  final String? labels = jsonConvert.convert<String>(json['Labels']);
  if (labels != null) {
    baseInfoEntity.labels = labels;
  }
  final String? street = jsonConvert.convert<String>(json['Street']);
  if (street != null) {
    baseInfoEntity.street = street;
  }
  final String? mapLocation = jsonConvert.convert<String>(json['MapLocation']);
  if (mapLocation != null) {
    baseInfoEntity.mapLocation = mapLocation;
  }
  final String? jobSeekerGroupCode = jsonConvert.convert<String>(
      json['JobSeekerGroupCode']);
  if (jobSeekerGroupCode != null) {
    baseInfoEntity.jobSeekerGroupCode = jobSeekerGroupCode;
  }
  return baseInfoEntity;
}

Map<String, dynamic> $BaseInfoEntityToJson(BaseInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['Name'] = entity.name;
  data['GenderCode'] = entity.genderCode;
  data['NationCode'] = entity.nationCode;
  data['MaritalStatusCode'] = entity.maritalStatusCode;
  data['Birthday'] = entity.birthday;
  data['Stature'] = entity.stature;
  data['Phone'] = entity.phone;
  data['Email'] = entity.email;
  data['QQ'] = entity.qQ;
  data['Address'] = entity.address;
  data['ComputerLevelCode'] = entity.computerLevelCode;
  data['EnglishLevelCode'] = entity.englishLevelCode;
  data['LiveAreaId'] = entity.liveAreaId;
  data['LiveAreaName'] = entity.liveAreaName;
  data['LiveAreaCascadeName'] = entity.liveAreaCascadeName;
  data['NativePlaceAreaId'] = entity.nativePlaceAreaId;
  data['NativePlaceAreaName'] = entity.nativePlaceAreaName;
  data['NativePlaceAreaCascadeName'] = entity.nativePlaceAreaCascadeName;
  data['EducationCode'] = entity.educationCode;
  data['WorkingAgeCode'] = entity.workingAgeCode;
  data['GraduateSchool'] = entity.graduateSchool;
  data['MajorIn'] = entity.majorIn;
  data['Labels'] = entity.labels;
  data['Street'] = entity.street;
  data['MapLocation'] = entity.mapLocation;
  data['JobSeekerGroupCode'] = entity.jobSeekerGroupCode;
  return data;
}

extension BaseInfoEntityExtension on BaseInfoEntity {
  BaseInfoEntity copyWith({
    String? name,
    String? genderCode,
    String? nationCode,
    String? maritalStatusCode,
    String? birthday,
    int? stature,
    String? phone,
    String? email,
    String? qQ,
    String? address,
    String? computerLevelCode,
    String? englishLevelCode,
    String? liveAreaId,
    String? liveAreaName,
    String? liveAreaCascadeName,
    String? nativePlaceAreaId,
    String? nativePlaceAreaName,
    String? nativePlaceAreaCascadeName,
    String? educationCode,
    String? workingAgeCode,
    String? graduateSchool,
    String? majorIn,
    String? labels,
    String? street,
    String? mapLocation,
    String? jobSeekerGroupCode,
  }) {
    return BaseInfoEntity()
      ..name = name ?? this.name
      ..genderCode = genderCode ?? this.genderCode
      ..nationCode = nationCode ?? this.nationCode
      ..maritalStatusCode = maritalStatusCode ?? this.maritalStatusCode
      ..birthday = birthday ?? this.birthday
      ..stature = stature ?? this.stature
      ..phone = phone ?? this.phone
      ..email = email ?? this.email
      ..qQ = qQ ?? this.qQ
      ..address = address ?? this.address
      ..computerLevelCode = computerLevelCode ?? this.computerLevelCode
      ..englishLevelCode = englishLevelCode ?? this.englishLevelCode
      ..liveAreaId = liveAreaId ?? this.liveAreaId
      ..liveAreaName = liveAreaName ?? this.liveAreaName
      ..liveAreaCascadeName = liveAreaCascadeName ?? this.liveAreaCascadeName
      ..nativePlaceAreaId = nativePlaceAreaId ?? this.nativePlaceAreaId
      ..nativePlaceAreaName = nativePlaceAreaName ?? this.nativePlaceAreaName
      ..nativePlaceAreaCascadeName = nativePlaceAreaCascadeName ??
          this.nativePlaceAreaCascadeName
      ..educationCode = educationCode ?? this.educationCode
      ..workingAgeCode = workingAgeCode ?? this.workingAgeCode
      ..graduateSchool = graduateSchool ?? this.graduateSchool
      ..majorIn = majorIn ?? this.majorIn
      ..labels = labels ?? this.labels
      ..street = street ?? this.street
      ..mapLocation = mapLocation ?? this.mapLocation
      ..jobSeekerGroupCode = jobSeekerGroupCode ?? this.jobSeekerGroupCode;
  }
}