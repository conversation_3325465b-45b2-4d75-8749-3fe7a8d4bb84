import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_kit/src/shared/locator.dart';
import 'package:flutter_kit/src/shared/services/storage/storage.dart';
import 'package:flutter_kit/src/shared/config/managers/config_manager.dart';

class AppInitializer {
  /// Initialize services, plugins, etc. before the app runs.
  Future<void> preAppRun() async {
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    await locator<Storage>().init();

    // 初始化配置管理器
    try {
      debugPrint('开始初始化配置管理器');
      await ConfigManager().initialize();
      debugPrint('配置管理器初始化完成');
    } catch (e) {
      debugPrint('配置管理器初始化失败: $e');
      // 不阻塞应用启动，配置管理器会在首次使用时重试
    }
  }

  /// Initialize services, plugins, etc. after the app runs.
  Future<void> postAppRun() async {
    // Hide RSOD in release mode.
    if (kReleaseMode) {
      ErrorWidget.builder = (FlutterErrorDetails details) => const SizedBox();
    }
  }
}
