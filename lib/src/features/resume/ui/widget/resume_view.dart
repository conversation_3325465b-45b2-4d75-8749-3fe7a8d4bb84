import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../base_info/ui/widget/build_divider.dart';
import '../../../base_info/ui/widget/get_image_provider.dart';
import 'selectable_item.dart';
import 'resume_pickers.dart';
import 'text_edit_dialog.dart';
import '../../theme/resume_theme.dart';



/// 简历数据模型
class ResumeData {
  String name;
  String? gender;
  String? birthYear;
  String jobStatus;
  String avatarUrl;
  List<WorkExperience> workExperiences;
  List<EducationExperience> educationExperiences;
  String personalAdvantage;
  List<Certificate> certificates;

  ResumeData({
    required this.name,
    this.gender,
    this.birthYear,
    required this.jobStatus,
    required this.avatarUrl,
    required this.workExperiences,
    required this.educationExperiences,
    required this.personalAdvantage,
    required this.certificates,
  });
}

class WorkExperience {
  final String company;
  final String position;
  final String period;
  final String description;

  WorkExperience({
    required this.company,
    required this.position,
    required this.period,
    required this.description,
  });
}

class EducationExperience {
  final String school;
  final String degree;
  final String major;
  final String period;

  EducationExperience({
    required this.school,
    required this.degree,
    required this.major,
    required this.period,
  });
}

class Certificate {
  final String name;
  final String date;

  Certificate({
    required this.name,
    required this.date,
  });
}

/// 简历编辑页面主视图
class ResumeEditView extends StatefulWidget {
  const ResumeEditView({super.key});

  @override
  State<ResumeEditView> createState() => _ResumeEditViewState();
}

class _ResumeEditViewState extends State<ResumeEditView> {
  late ResumeData _resumeData;

  @override
  void initState() {
    super.initState();
    _initializeResumeData();
  }

  void _initializeResumeData() {
    _resumeData = ResumeData(
      name: "",
      gender: "",
      birthYear: "",
      jobStatus: "",
      avatarUrl: '',
      workExperiences: [
        // WorkExperience(
        //   company: "某某科技有限公司",
        //   position: "UI设计师",
        //   period: "2022.07 - 至今",
        //   description: "1. 负责公司产品线的UI设计，包括移动端和Web端。\n2. 参与产品前期讨论，输出交互原型和高保真视觉稿。\n3. 制定和维护设计规范，确保产品视觉统一性。",
        // ),
      ],
      educationExperiences: [
        // EducationExperience(
        //   school: "某某美术学院",
        //   degree: "本科",
        //   major: "视觉传达设计",
        //   period: "2018.09 - 2022.06",
        // ),
      ],
      personalAdvantage: "",
      certificates: [
        // Certificate(
        //   name: "大学英语六级(CET-6)",
        //   date: "2021.06",
        // ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ResumeTheme.backgroundColor,
      body: Stack(
        children: [
          // 背景渐变 - 延伸到状态栏
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 200.h + MediaQuery.of(context).padding.top,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    ResumeTheme.primaryColor.withValues(alpha: 0.08),
                    ResumeTheme.primaryColor.withValues(alpha: 0.04),
                    ResumeTheme.primaryColor.withValues(alpha: 0.02),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
          // 主要内容
          SafeArea(
            child: Column(
              children: [
                _buildAppBar(),
                Expanded(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.all(16.w),
                    child: Column(
                      children: [
                        _buildPersonalInfoSection(),
                        SizedBox(height: 16.h),
                        _buildWorkExperienceSection(),
                        SizedBox(height: 16.h),
                        _buildEducationExperienceSection(),
                        SizedBox(height: 16.h),
                        _buildPersonalAdvantageSection(),
                        SizedBox(height: 16.h),
                        _buildCertificateSection(),
                        SizedBox(height: 32.h),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建应用栏
  Widget _buildAppBar() {
    return Container(
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 16.h),
      color: Colors.transparent,
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              context.router.pop();
            },
            child: Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: ResumeTheme.surfaceColor.withValues(alpha: 0.9),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.arrow_back_ios_new,
                size: 18.w,
                color: ResumeTheme.textPrimary,
              ),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Text(
              '编辑在线简历',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
                color: ResumeTheme.textPrimary,
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              context.router.pop();
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: ResumeTheme.primaryColor,
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: Text(
                '保存',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建个人信息模块
  Widget _buildPersonalInfoSection() {
    return _buildStyledCard(
      title: '个人信息',
      child: Column(
        children: [
          _buildAvatarRow(),
          buildDivider(),
          SelectableItem(
            label: '姓名',
            value: _resumeData.name,
            hint: '请输入姓名',
            hintColor: const Color(0xFFCCCCCC),
            isRequired: true,
            onTap: () {
              TextEditDialog.show(
                context: context,
                title: '编辑姓名',
                initialText: _resumeData.name,
                maxLength: 20,
                maxLines: 1,
                hintText: '请输入您的姓名',
                heightRatio: 0.7, // 姓名编辑使用较小高度70%
                onSave: (text) {
                  setState(() {
                    _resumeData.name = text;
                  });
                },
              );
            },
          ),
          buildDivider(),
          ResumePickers.xmlPicker(
            context: context,
            currentValue: _resumeData.gender,
            groundName: 'gender',
            label: '性别',
            title: '选择性别',
            onChanged: (value) {
              setState(() => _resumeData.gender = value);
            },
          ),
          buildDivider(),
          ResumePickers.birthYearPicker(
            context: context,
            currentValue: _resumeData.birthYear,
            onChanged: (value) {
              setState(() => _resumeData.birthYear = value);
            },
          ),
          buildDivider(),
          ResumePickers.jobStatusPicker(
            context: context,
            currentValue: _resumeData.jobStatus,
            onChanged: (value) {
              setState(() => _resumeData.jobStatus = value);
            },
          ),
        ],
      ),
    );
  }

  /// 构建工作经历模块
  Widget _buildWorkExperienceSection() {
    return _buildStyledCard(
      title: '工作经历',
      actionText: '+ 添加',
      onActionTap: () {
        // TODO: 处理添加工作经历
      },
      child: _resumeData.workExperiences.isEmpty
          ? _buildEmptyState('补充工作经历，获得更多工作机会！') // 🔥 空状态提示
          : Column(
              children: _resumeData.workExperiences.map((experience) {
                return _buildExperienceItem(
                  title: experience.company,
                  period: experience.period,
                  subtitle: experience.position,
                  description: experience.description,
                );
              }).toList(),
            ),
    );
  }

  /// 构建教育经历模块
  Widget _buildEducationExperienceSection() {
    return _buildStyledCard(
      title: '教育经历',
      actionText: '+ 添加',
      onActionTap: () {
        // TODO: 处理添加教育经历
      },
      child: _resumeData.educationExperiences.isEmpty
          ? _buildEmptyState('补充教育经历，展示学习背景！') // 🔥 空状态提示
          : Column(
              children: _resumeData.educationExperiences.map((education) {
                return _buildExperienceItem(
                  title: education.school,
                  period: education.period,
                  subtitle: '${education.degree} · ${education.major}',
                );
              }).toList(),
            ),
    );
  }

  /// 构建个人优势模块
  Widget _buildPersonalAdvantageSection() {
    return _buildStyledCard(
      title: '个人优势',
      actionText: '编辑',
      onActionTap: () {
        TextEditDialog.show(
          context: context,
          title: '编辑个人优势',
          initialText: _resumeData.personalAdvantage,
          maxLength: 500,
          maxLines: 8,
          hintText: '请描述您的个人优势、技能特长、工作经验等...',
          heightRatio: 0.85, // 个人优势编辑使用较大高度85%
          onSave: (text) {
            setState(() {
              _resumeData.personalAdvantage = text;
            });
          },
        );
      },
      child: _resumeData.personalAdvantage.isEmpty
          ? _buildEmptyState('补充个人优势，突出专业能力！') // 🔥 空状态提示
          : _buildExperienceItem(
              description: _resumeData.personalAdvantage,
            ),
    );
  }

  /// 构建资格证书模块
  Widget _buildCertificateSection() {
    return _buildStyledCard(
      title: '资格证书',
      actionText: '+ 添加',
      onActionTap: () {
        // TODO: 处理添加证书
      },
      child: _resumeData.certificates.isEmpty
          ? _buildEmptyState('补充资格证书，提升竞争优势！') // 🔥 空状态提示
          : Column(
              children: _resumeData.certificates.map((certificate) {
                return _buildExperienceItem(
                  title: certificate.name,
                  period: certificate.date,
                );
              }).toList(),
            ),
    );
  }

  /// 构建头像行
  Widget _buildAvatarRow() {
    return GestureDetector(
      onTap: () {
        _showAvatarPicker();
      },
      behavior: HitTestBehavior.opaque, // 确保整个区域都可以点击
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中对齐
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '头像',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: ResumeTheme.textPrimary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 7.h),
                  Text(
                    '点击修改头像',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: ResumeTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: 8.w), // 添加间距
            Container(
              width: 56.w,
              height: 56.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                image: DecorationImage(
                  image: getImageProvider(_resumeData.avatarUrl),
                  fit: BoxFit.cover,
                ),
                border: Border.all(
                  color: ResumeTheme.borderColor,
                  width: 1,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }



  /// 构建经历项
  Widget _buildExperienceItem({
    String? title,
    String? period,
    String? subtitle,
    String? description,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: ResumeTheme.textPrimary,
                    ),
                  ),
                ),
                if (period != null)
                  Text(
                    period,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: ResumeTheme.textSecondary,
                    ),
                  ),
              ],
            ),
          if (subtitle != null) ...[
            SizedBox(height: 4.h),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 14.sp,
                color: ResumeTheme.textSecondary,
              ),
            ),
          ],
          if (description != null) ...[
            SizedBox(height: 12.h),
            Text(
              description,
              style: TextStyle(
                fontSize: 14.sp,
                color: ResumeTheme.textPrimary,
                height: 1.5,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 统一样式的卡片容器
  Widget _buildStyledCard({
    required String title,
    required Widget child,
    String? actionText,
    VoidCallback? onActionTap,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: ResumeTheme.surfaceColor,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: ResumeTheme.textPrimary,
                  ),
                ),
                if (actionText != null)
                  GestureDetector(
                    onTap: onActionTap,
                    child: Text(
                      actionText,
                      style: TextStyle(
                        color: ResumeTheme.primaryColor,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  )
              ],
            ),
          ),
          Container(
            height: 1.h,
            color: ResumeTheme.borderColor,
          ),
          child,
        ],
      ),
    );
  }



  /// 构建空状态提示
  Widget _buildEmptyState(String message) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 20.h),
      child: Text(
        message,
        style: TextStyle(
          fontSize: 14.sp,
          color: const Color(0xFFCCCCCC), // 🔥 使用#cccccc颜色
          fontStyle: FontStyle.normal,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }



  /// 显示头像选择器
  void _showAvatarPicker() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: ResumeTheme.surfaceColor,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16.r),
              topRight: Radius.circular(16.r),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 头部标题
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text(
                        '取消',
                        style: TextStyle(color: ResumeTheme.textSecondary),
                      ),
                    ),
                    Text(
                      '选择头像',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w600,
                        color: ResumeTheme.textPrimary,
                      ),
                    ),
                    SizedBox(width: 48.w), // 占位，保持标题居中
                  ],
                ),
              ),
              Container(height: 1.h, color: ResumeTheme.borderColor),

              // 默认头像选项
              Padding(
                padding: EdgeInsets.all(20.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 16.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildAvatarOption(
                          'assets/images/ic_head_kt1.png',
                          isAsset: true,
                        ),
                        _buildAvatarOption(
                          'assets/images/ic_head_kt2.png',
                          isAsset: true,
                        ),
                        _buildUploadAvatarOption(),
                      ],
                    ),
                    SizedBox(height: 30.h),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建头像选项
  Widget _buildAvatarOption(String imagePath, {bool isAsset = false}) {
    final isSelected = _resumeData.avatarUrl == imagePath;
    return GestureDetector(
      onTap: () {
        setState(() {
          _resumeData.avatarUrl = imagePath;
        });
        Navigator.pop(context);
      },
      child: Column(
        children: [
          Container(
            width: 70.w,
            height: 70.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              image: DecorationImage(
                image: isAsset
                    ? AssetImage(imagePath) as ImageProvider
                    : NetworkImage(imagePath),
                fit: BoxFit.cover,
              ),
              border: Border.all(
                color: isSelected ? ResumeTheme.primaryColor : ResumeTheme.borderColor,
                width: isSelected ? 3 : 1,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建上传头像选项（圆形虚线边框 + 加号）
  Widget _buildUploadAvatarOption() {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
        _showImageSourcePicker();
      },
      child: Column(
        children: [
          Container(
            width: 70.w,
            height: 70.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.transparent, // 透明边框，使用CustomPainter绘制虚线
                width: 2,
              ),
            ),
            child: CustomPaint(
              painter: DashedCirclePainter(
                color: ResumeTheme.primaryColor.withValues(alpha: 0.6),
                strokeWidth: 2,
              ),
              child: Center(
                child: Icon(
                  Icons.add,
                  size: 24.w,
                  color: ResumeTheme.primaryColor.withValues(alpha: 0.8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }






  /// 显示图片来源选择器
  void _showImageSourcePicker() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: ResumeTheme.surfaceColor,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16.r),
              topRight: Radius.circular(16.r),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 头部标题
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text(
                        '取消',
                        style: TextStyle(color: ResumeTheme.textSecondary),
                      ),
                    ),
                    Text(
                      '选择图片来源',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w600,
                        color: ResumeTheme.textPrimary,
                      ),
                    ),
                    SizedBox(width: 48.w), // 占位，保持标题居中
                  ],
                ),
              ),
              Container(height: 1.h, color: ResumeTheme.borderColor),
            ],
          ),
        );
      },
    );
  }

  /// 从相机拍照
  void _pickImageFromCamera() {
    // TODO: 实现相机拍照功能
    // 可以使用 image_picker 插件
    print('从相机拍照');
  }

  /// 从相册选择
  void _pickImageFromGallery() {
    // TODO: 实现相册选择功能
    // 可以使用 image_picker 插件
    print('从相册选择');
  }
}

/// 虚线圆形画笔
class DashedCirclePainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashWidth;
  final double dashSpace;

  DashedCirclePainter({
    required this.color,
    required this.strokeWidth,
    this.dashWidth = 4.0,
    this.dashSpace = 4.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    // 计算圆周长
    final circumference = 2 * 3.14159 * radius;
    // 计算虚线段数
    final dashCount = (circumference / (dashWidth + dashSpace)).floor();
    final adjustedDashWidth = circumference / dashCount / 2;
    final adjustedDashSpace = adjustedDashWidth;

    // 绘制虚线圆
    for (int i = 0; i < dashCount; i++) {
      final startAngle = (i * 2 * adjustedDashWidth / radius);
      final sweepAngle = adjustedDashWidth / radius;

      final path = Path();
      path.addArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
      );
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}


