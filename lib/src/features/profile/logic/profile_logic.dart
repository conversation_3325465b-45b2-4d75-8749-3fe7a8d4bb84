import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/logic/view_state_logic.dart';
import 'package:flutter_kit/src/base/core/view_state.dart';
import 'package:flutter_kit/src/datasource/models/user_account_entity.dart';
import 'package:flutter_kit/src/datasource/repositories/profile_repository.dart';
import 'package:flutter_kit/src/features/login/logic/examinee_helper.dart';
import 'package:flutter_kit/src/shared/locator.dart';

/// 菜单项模型
class ProfileMenuItem {
  final String title;
  final IconData icon;

  ProfileMenuItem({
    required this.title,
    required this.icon,
  });
}


/// 我的页面逻辑
class ProfileLogic extends ViewStateLogic {
  final ProfileRepository repository;
  UserAccountEntity? _userAccount;
  List<ProfileMenuItem> _menuItems = [];
  bool _isLoggedIn = false; // 登录状态

  UserAccountEntity? get userAccount => _userAccount;
  List<ProfileMenuItem> get menuItems => _menuItems;
  bool get isLoggedIn => _isLoggedIn;

  // 便捷的状态访问方法
  bool get isLoading => viewState is ViewStateLoading;
  bool get hasError => viewState is ViewStateError;
  bool get hasData => viewState is ViewStateSuccess;

  ProfileLogic({required this.repository}) {
    // 初始化菜单项，不依赖用户数据
    _initMenuItems();
    // 检查登录状态并加载数据
    _checkLoginAndLoadData();
  }

  /// 检查登录状态并加载数据
  void _checkLoginAndLoadData() async {
    final examineeHelper = locator<ExamineeHelper>();
    _isLoggedIn = await examineeHelper.isLogin();

    if (_isLoggedIn) {
      loadData();
    } else {
      // 如果未登录，通知UI更新
      notifyListeners();
    }
  }
  
  @override
  void loadData() {
    if (!_isLoggedIn) return;

    // 使用 GetCurrentUser 接口获取用户信息
    sendRequest<UserAccountEntity>(
      repository.getCurrentUser(),
      successCallback: (data) {
        _userAccount = data;
      },
    );
  }

  /// 初始化菜单项
  void _initMenuItems() {
    _menuItems = [
      ProfileMenuItem(
        title: '账号管理',
        icon: Icons.person_outline,
      ),
      ProfileMenuItem(
        title: '隐私设置',
        icon: Icons.lock_outline,
      ),
      ProfileMenuItem(
        title: '帮助与反馈',
        icon: Icons.help_outline,
      ),
      ProfileMenuItem(
        title: '关于我们',
        icon: Icons.info_outline,
      ),

    ];
  }
  
  /// 刷新数据
  void refreshData() {
    loadData();
  }
  
  /// 登录成功后刷新状态
  void onLoginSuccess() {
    _checkLoginAndLoadData();
  }

  /// 退出登录
  void logout() async {
    final examineeHelper = locator<ExamineeHelper>();
    await examineeHelper.logout();

    _isLoggedIn = false;
    _userAccount = null;
    notifyListeners();
  }
}
