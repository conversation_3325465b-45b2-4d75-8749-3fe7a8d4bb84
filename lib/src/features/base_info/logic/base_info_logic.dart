import 'package:flutter_kit/src/base/base.dart';
import 'package:flutter_kit/src/datasource/models/base_info_entity.dart';
import 'package:flutter_kit/src/datasource/repositories/base_info_repository.dart';

class BaseInfoLogic extends ViewStateLogic {
  final BaseInfoRepository repository;
  BaseInfoEntity? _baseInfo;

  BaseInfoEntity? get baseInfoEntity => _baseInfo;

  BaseInfoLogic({required this.repository}) {}

  @override
  void loadData() {
    sendRequest<BaseInfoEntity>(repository.getBaseInfo(),
        successCallback: (data) {
      _baseInfo = data;
    });
  }
}
