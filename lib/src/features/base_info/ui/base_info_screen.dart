



import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_kit/src/base/base.dart';
import 'package:flutter_kit/src/features/base_info/logic/base_info_logic.dart';
import 'package:flutter_kit/src/features/base_info/ui/widget/base_info_page_view.dart';
import 'package:flutter_kit/src/shared/locator.dart';

import '../../profile/logic/profile_logic.dart';

@RoutePage()
class BaseInfoScreen extends ViewStateWidget<BaseInfoLogic>{
  @override
  Widget buildBody(BuildContext context, BaseInfoLogic logic) {
    return const BaseInfoPageView();
  }

  @override
  PreferredSizeWidget? buildAppBar(BuildContext context, BaseInfoLogic logic) {
    return null; // 不使用默认AppBar，在ProfilePageView中自定义
  }

  @override
  BaseInfoLogic createController() {
    return locator<BaseInfoLogic>();
  }

  @override
  bool useScaffold() => false; // 不使用外层Scaffold，让ProfilePageView自己处理


}