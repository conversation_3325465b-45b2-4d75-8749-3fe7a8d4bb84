import 'package:flutter/foundation.dart';
import '../models/config_models.dart';
import '../services/config_service.dart';

/// 配置管理器
/// 提供key↔value转换的公共接口
class ConfigManager {
  static final ConfigManager _instance = ConfigManager._internal();

  factory ConfigManager() => _instance;

  ConfigManager._internal();

  final ConfigService _configService = ConfigService();
  ConfigConstant? _config;

  /// 初始化配置管理器
  Future<void> initialize() async {
    try {
      debugPrint('初始化配置管理器');
      await _configService.initializeConfig();
      _config = await _configService.getCurrentConfig();
      debugPrint('配置管理器初始化完成，版本: ${_config?.version}');
    } catch (e) {
      debugPrint('配置管理器初始化失败: $e');
    }
  }

  /// 确保配置已加载
  Future<void> _ensureConfigLoaded() async {
    if (_config == null) {
      await initialize();
    }
  }

  /// 通过key获取value
  Future<String?> getValueByKey(String groupName, String key) async {
    try {
      await _ensureConfigLoaded();

      final value = _config?.getValueByKey(groupName, key);
      debugPrint('getValueByKey: $groupName.$key -> $value');
      return value;
    } catch (e) {
      debugPrint('通过key获取value失败: $e');
      return null;
    }
  }

  /// 通过value获取key
  Future<String?> getKeyByValue(String groupName, String value) async {
    if (value.isEmpty) {
      return null;
    }
    try {
      await _ensureConfigLoaded();

      final key = _config?.getKeyByValue(groupName, value);
      debugPrint('getKeyByValue: $groupName.$value -> $key');
      return key;
    } catch (e) {
      debugPrint('通过value获取key失败: $e');
      return null;
    }
  }

  /// 获取整个配置组
  Future<List<ConfigItem>?> getConfigGroup(String groupName) async {
    try {
      await _ensureConfigLoaded();

      final group = _config?.getGroupByName(groupName);
      debugPrint('getConfigGroup: $groupName -> ${group?.items.length} items');
      return group?.items;
    } catch (e) {
      debugPrint('获取配置组失败: $e');
      return null;
    }
  }

  /// 获取配置组的所有选项（用于选择器）
  Future<Map<String, String>?> getGroupOptions(String groupName) async {
    try {
      await _ensureConfigLoaded();

      final group = _config?.getGroupByName(groupName);
      if (group == null) return null;

      final options = <String, String>{};
      for (final item in group.items) {
        options[item.key] = item.value;
      }

      debugPrint('getGroupOptions: $groupName -> ${options.length} options');
      return options;
    } catch (e) {
      debugPrint('获取配置组选项失败: $e');
      return null;
    }
  }

  /// 获取配置组的所有选项列表（适合List<String>类型）
  Future<List<String>?> getGroupOptionsList(String groupName) async {
    try {
      await _ensureConfigLoaded();

      final group = _config?.getGroupByName(groupName);
      if (group == null) return null;

      final options = <String>[];
      for (final item in group.items) {
        options.add(item.value);
      }

      debugPrint(
          'getGroupOptionsList: $groupName -> ${options.length} options');
      return options;
    } catch (e) {
      debugPrint('获取配置组选项列表失败: $e');
      return null;
    }
  }

  /// 获取配置组的所有值列表（用于显示）
  Future<List<String>?> getGroupValues(String groupName) async {
    try {
      await _ensureConfigLoaded();

      final group = _config?.getGroupByName(groupName);
      final values = group?.values;

      debugPrint('getGroupValues: $groupName -> ${values?.length} values');
      return values;
    } catch (e) {
      debugPrint('获取配置组值列表失败: $e');
      return null;
    }
  }

  /// 检查配置组是否存在
  Future<bool> hasGroup(String groupName) async {
    try {
      await _ensureConfigLoaded();

      final hasGroup = _config?.getGroupByName(groupName) != null;
      debugPrint('hasGroup: $groupName -> $hasGroup');
      return hasGroup;
    } catch (e) {
      debugPrint('检查配置组存在性失败: $e');
      return false;
    }
  }

  /// 检查配置项是否存在
  Future<bool> hasConfigItem(String groupName, String key) async {
    try {
      await _ensureConfigLoaded();

      final group = _config?.getGroupByName(groupName);
      final hasItem = group?.getItemByKey(key) != null;

      debugPrint('hasConfigItem: $groupName.$key -> $hasItem');
      return hasItem;
    } catch (e) {
      debugPrint('检查配置项存在性失败: $e');
      return false;
    }
  }

  /// 刷新配置
  Future<bool> refreshConfig() async {
    try {
      debugPrint('刷新配置');

      final success = await _configService.forceRefreshConfig();
      if (success) {
        _config = await _configService.getCurrentConfig();
        debugPrint('配置刷新成功，版本: ${_config?.version}');
      }

      return success;
    } catch (e) {
      debugPrint('刷新配置失败: $e');
      return false;
    }
  }

  /// 获取配置状态
  Future<Map<String, dynamic>> getStatus() async {
    try {
      final serviceStatus = await _configService.getConfigStatus();

      return {
        'managerInitialized': _config != null,
        'configVersion': _config?.version,
        'groupCount': _config?.groups.length ?? 0,
        'groupNames': _config?.groupNames ?? [],
        'service': serviceStatus,
      };
    } catch (e) {
      debugPrint('获取配置状态失败: $e');
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// 清除配置缓存
  Future<bool> clearCache() async {
    try {
      debugPrint('清除配置缓存');

      final success = await _configService.clearAllConfig();
      if (success) {
        _config = null;
        debugPrint('配置缓存清除成功');
      }

      return success;
    } catch (e) {
      debugPrint('清除配置缓存失败: $e');
      return false;
    }
  }

  /// 批量转换：keys -> values
  Future<List<String>> convertKeysToValues(
      String groupName, List<String> keys) async {
    final values = <String>[];

    for (final key in keys) {
      final value = await getValueByKey(groupName, key);
      if (value != null) {
        values.add(value);
      }
    }

    return values;
  }

  /// 批量转换：values -> keys
  Future<List<String>> convertValuesToKeys(
      String groupName, List<String> values) async {
    final keys = <String>[];

    for (final value in values) {
      final key = await getKeyByValue(groupName, value);
      if (key != null) {
        keys.add(key);
      }
    }

    return keys;
  }

  /// 获取当前配置版本
  String get version => _config?.version ?? '0.0.0';

  /// 是否已初始化
  bool get isInitialized => _config != null;

  /// 获取所有组名
  List<String> get groupNames => _config?.groupNames ?? [];
}
