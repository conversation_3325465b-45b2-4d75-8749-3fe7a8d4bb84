import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/config_models.dart';

/// 配置本地存储服务
/// 负责XML内容和版本信息的本地存储和读取
class ConfigStorage {
  static const String _xmlContentKey = 'config_xml_content';
  static const String _versionKey = 'config_version';
  static const String _lastUpdateKey = 'config_last_update';
  static const String _configFileName = 'app_config.xml';
  static const String _configDataFileName = 'config_data.json';

  /// 保存XML内容到本地
  Future<bool> saveXmlContent(String xmlContent) async {
    try {
      // 方式1：使用SharedPreferences存储（适合小文件）
      final prefs = await SharedPreferences.getInstance();
      final success1 = await prefs.setString(_xmlContent<PERSON>ey, xmlContent);
      
      // 方式2：使用文件存储（适合大文件，作为备份）
      final success2 = await _saveXmlToFile(xmlContent);
      
      // 更新最后更新时间
      await prefs.setInt(_lastUpdateKey, DateTime.now().millisecondsSinceEpoch);
      
      return success1 && success2;
    } catch (e) {
      debugPrint('保存XML内容失败: $e');
      return false;
    }
  }

  /// 从本地读取XML内容
  Future<String?> getXmlContent() async {
    try {
      // 优先从SharedPreferences读取
      final prefs = await SharedPreferences.getInstance();
      String? xmlContent = prefs.getString(_xmlContentKey);
      
      // 如果SharedPreferences中没有，尝试从文件读取
      if (xmlContent == null || xmlContent.isEmpty) {
        xmlContent = await _getXmlFromFile();
      }
      
      return xmlContent;
    } catch (e) {
      debugPrint('读取XML内容失败: $e');
      return null;
    }
  }

  /// 保存版本信息
  Future<bool> saveVersion(String version) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setString(_versionKey, version);
    } catch (e) {
      debugPrint('保存版本信息失败: $e');
      return false;
    }
  }

  /// 获取本地版本
  Future<String?> getLocalVersion() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_versionKey);
    } catch (e) {
      debugPrint('读取版本信息失败: $e');
      return null;
    }
  }

  /// 保存解析后的配置数据（JSON格式，用于快速访问）
  Future<bool> saveConfigData(ConfigConstant config) async {
    try {
      final configJson = jsonEncode(config.toMap());
      
      // 保存到SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final success1 = await prefs.setString('config_data', configJson);
      
      // 保存到文件
      final success2 = await _saveConfigDataToFile(configJson);
      
      return success1 && success2;
    } catch (e) {
      debugPrint('保存配置数据失败: $e');
      return false;
    }
  }

  /// 读取解析后的配置数据
  Future<ConfigConstant?> getConfigData() async {
    try {
      // 优先从SharedPreferences读取
      final prefs = await SharedPreferences.getInstance();
      String? configJson = prefs.getString('config_data');
      
      // 如果SharedPreferences中没有，尝试从文件读取
      if (configJson == null || configJson.isEmpty) {
        configJson = await _getConfigDataFromFile();
      }
      
      if (configJson != null && configJson.isNotEmpty) {
        final configMap = jsonDecode(configJson) as Map<String, dynamic>;
        return ConfigConstant.fromMap(configMap);
      }
      
      return null;
    } catch (e) {
      debugPrint('读取配置数据失败: $e');
      return null;
    }
  }

  /// 获取最后更新时间
  Future<DateTime?> getLastUpdateTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt(_lastUpdateKey);
      if (timestamp != null) {
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      }
      return null;
    } catch (e) {
      debugPrint('读取最后更新时间失败: $e');
      return null;
    }
  }

  /// 检查是否需要更新（基于时间）
  Future<bool> shouldUpdate({Duration maxAge = const Duration(hours: 24)}) async {
    try {
      final lastUpdate = await getLastUpdateTime();
      if (lastUpdate == null) {
        return true; // 从未更新过
      }
      
      final now = DateTime.now();
      final difference = now.difference(lastUpdate);
      
      return difference > maxAge;
    } catch (e) {
      debugPrint('检查更新时间失败: $e');
      return true; // 出错时默认需要更新
    }
  }

  /// 清除所有配置数据
  Future<bool> clearAll() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 清除SharedPreferences中的数据
      await prefs.remove(_xmlContentKey);
      await prefs.remove(_versionKey);
      await prefs.remove(_lastUpdateKey);
      await prefs.remove('config_data');
      
      // 清除文件中的数据
      await _deleteConfigFiles();
      
      return true;
    } catch (e) {
      debugPrint('清除配置数据失败: $e');
      return false;
    }
  }

  /// 获取存储状态信息
  Future<Map<String, dynamic>> getStorageInfo() async {
    try {
      final hasXmlContent = await getXmlContent() != null;
      final version = await getLocalVersion();
      final lastUpdate = await getLastUpdateTime();
      final hasConfigData = await getConfigData() != null;
      
      return {
        'hasXmlContent': hasXmlContent,
        'version': version,
        'lastUpdate': lastUpdate?.toIso8601String(),
        'hasConfigData': hasConfigData,
        'shouldUpdate': await shouldUpdate(),
      };
    } catch (e) {
      debugPrint('获取存储状态失败: $e');
      return {};
    }
  }

  // 私有方法：文件操作

  /// 保存XML到文件
  Future<bool> _saveXmlToFile(String xmlContent) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$_configFileName');
      await file.writeAsString(xmlContent);
      return true;
    } catch (e) {
      debugPrint('保存XML文件失败: $e');
      return false;
    }
  }

  /// 从文件读取XML
  Future<String?> _getXmlFromFile() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$_configFileName');
      
      if (await file.exists()) {
        return await file.readAsString();
      }
      
      return null;
    } catch (e) {
      debugPrint('读取XML文件失败: $e');
      return null;
    }
  }

  /// 保存配置数据到文件
  Future<bool> _saveConfigDataToFile(String configJson) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$_configDataFileName');
      await file.writeAsString(configJson);
      return true;
    } catch (e) {
      debugPrint('保存配置数据文件失败: $e');
      return false;
    }
  }

  /// 从文件读取配置数据
  Future<String?> _getConfigDataFromFile() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$_configDataFileName');
      
      if (await file.exists()) {
        return await file.readAsString();
      }
      
      return null;
    } catch (e) {
      debugPrint('读取配置数据文件失败: $e');
      return null;
    }
  }

  /// 删除配置文件
  Future<void> _deleteConfigFiles() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      
      final xmlFile = File('${directory.path}/$_configFileName');
      if (await xmlFile.exists()) {
        await xmlFile.delete();
      }
      
      final dataFile = File('${directory.path}/$_configDataFileName');
      if (await dataFile.exists()) {
        await dataFile.delete();
      }
    } catch (e) {
      debugPrint('删除配置文件失败: $e');
    }
  }
}
