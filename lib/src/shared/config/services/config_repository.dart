import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../../../datasource/http/dio_config.dart';
import '../../../shared/locator.dart';

/// 配置网络请求服务
/// 负责从远程API获取XML配置文件
class ConfigRepository {
  static const String _configUrl = 'https://www.zdzp.cn/Config/Constant.xml';
  
  final Dio _dio;

  ConfigRepository({Dio? dio}) : _dio = dio ?? locator<DioConfig>().dio;

  /// 获取远程XML配置
  Future<String> fetchRemoteConfig() async {
    try {
      debugPrint('开始获取远程配置: $_configUrl');
      
      final response = await _dio.get(
        _configUrl,
        options: Options(
          responseType: ResponseType.plain, // 获取纯文本响应
          headers: {
            'Accept': 'application/xml, text/xml, */*',
            'User-Agent': 'Flutter App Config Client',
          },
          // 设置超时时间
          receiveTimeout: const Duration(seconds: 30),
          sendTimeout: const Duration(seconds: 10),
        ),
      );

      if (response.statusCode == 200) {
        final xmlContent = response.data as String;
        
        if (xmlContent.isEmpty) {
          throw Exception('获取到的XML内容为空');
        }
        
        debugPrint('成功获取远程配置，内容长度: ${xmlContent.length}');
        return xmlContent;
      } else {
        throw Exception('HTTP请求失败: ${response.statusCode}');
      }
    } on DioException catch (e) {
      debugPrint('网络请求异常: ${e.message}');
      
      switch (e.type) {
        case DioExceptionType.connectionTimeout:
          throw Exception('连接超时，请检查网络连接');
        case DioExceptionType.receiveTimeout:
          throw Exception('接收数据超时，请重试');
        case DioExceptionType.sendTimeout:
          throw Exception('发送请求超时，请重试');
        case DioExceptionType.badResponse:
          throw Exception('服务器响应错误: ${e.response?.statusCode}');
        case DioExceptionType.cancel:
          throw Exception('请求已取消');
        case DioExceptionType.connectionError:
          throw Exception('网络连接错误，请检查网络设置');
        case DioExceptionType.badCertificate:
          throw Exception('证书验证失败');
        case DioExceptionType.unknown:
        default:
          throw Exception('网络请求失败: ${e.message}');
      }
    } catch (e) {
      debugPrint('获取远程配置失败: $e');
      throw Exception('获取远程配置失败: $e');
    }
  }

  /// 检查远程版本（通过HEAD请求获取Last-Modified等信息）
  Future<Map<String, String>> getRemoteHeaders() async {
    try {
      debugPrint('检查远程配置头信息');
      
      final response = await _dio.head(
        _configUrl,
        options: Options(
          headers: {
            'User-Agent': 'Flutter App Config Client',
          },
          receiveTimeout: const Duration(seconds: 10),
        ),
      );

      if (response.statusCode == 200) {
        final headers = <String, String>{};
        
        // 提取有用的头信息
        response.headers.forEach((name, values) {
          if (values.isNotEmpty) {
            headers[name.toLowerCase()] = values.first;
          }
        });
        
        debugPrint('远程配置头信息: $headers');
        return headers;
      } else {
        throw Exception('HEAD请求失败: ${response.statusCode}');
      }
    } on DioException catch (e) {
      debugPrint('检查远程头信息异常: ${e.message}');
      throw Exception('检查远程配置失败: ${e.message}');
    } catch (e) {
      debugPrint('检查远程头信息失败: $e');
      throw Exception('检查远程配置失败: $e');
    }
  }

  /// 检查远程配置是否有更新（基于Last-Modified）
  Future<bool> hasRemoteUpdate({DateTime? lastLocalUpdate}) async {
    try {
      if (lastLocalUpdate == null) {
        return true; // 如果没有本地更新时间，认为需要更新
      }
      
      final headers = await getRemoteHeaders();
      final lastModified = headers['last-modified'];
      
      if (lastModified == null) {
        // 如果服务器不提供Last-Modified，基于时间判断
        final now = DateTime.now();
        final difference = now.difference(lastLocalUpdate);
        return difference > const Duration(hours: 24); // 24小时后强制更新
      }
      
      // 解析Last-Modified时间
      final remoteModified = DateTime.tryParse(lastModified);
      if (remoteModified == null) {
        return true; // 解析失败，默认需要更新
      }
      
      // 比较时间
      return remoteModified.isAfter(lastLocalUpdate);
    } catch (e) {
      debugPrint('检查远程更新失败: $e');
      return false; // 检查失败，不强制更新
    }
  }

  /// 测试网络连接
  Future<bool> testConnection() async {
    try {
      debugPrint('测试网络连接');
      
      final response = await _dio.head(
        _configUrl,
        options: Options(
          receiveTimeout: const Duration(seconds: 5),
        ),
      );
      
      final isConnected = response.statusCode == 200;
      debugPrint('网络连接测试结果: $isConnected');
      return isConnected;
    } catch (e) {
      debugPrint('网络连接测试失败: $e');
      return false;
    }
  }

  /// 获取配置URL
  String get configUrl => _configUrl;

  /// 设置自定义配置URL（用于测试或其他环境）
  static String? _customConfigUrl;
  
  static void setCustomConfigUrl(String? url) {
    _customConfigUrl = url;
  }
  
  String get _effectiveConfigUrl => _customConfigUrl ?? _configUrl;

  /// 下载配置文件并保存到指定路径（可选功能）
  Future<void> downloadConfigToFile(String savePath) async {
    try {
      debugPrint('下载配置文件到: $savePath');
      
      await _dio.download(
        _effectiveConfigUrl,
        savePath,
        options: Options(
          headers: {
            'Accept': 'application/xml, text/xml, */*',
            'User-Agent': 'Flutter App Config Client',
          },
          receiveTimeout: const Duration(seconds: 60), // 下载可能需要更长时间
        ),
      );
      
      debugPrint('配置文件下载完成');
    } on DioException catch (e) {
      debugPrint('下载配置文件异常: ${e.message}');
      throw Exception('下载配置文件失败: ${e.message}');
    } catch (e) {
      debugPrint('下载配置文件失败: $e');
      throw Exception('下载配置文件失败: $e');
    }
  }

  /// 获取网络状态信息
  Future<Map<String, dynamic>> getNetworkInfo() async {
    try {
      final startTime = DateTime.now();
      final isConnected = await testConnection();
      final endTime = DateTime.now();
      final responseTime = endTime.difference(startTime).inMilliseconds;
      
      Map<String, String>? headers;
      if (isConnected) {
        try {
          headers = await getRemoteHeaders();
        } catch (e) {
          debugPrint('获取头信息失败: $e');
        }
      }
      
      return {
        'isConnected': isConnected,
        'responseTime': responseTime,
        'configUrl': _effectiveConfigUrl,
        'headers': headers,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('获取网络信息失败: $e');
      return {
        'isConnected': false,
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
}
